// lobbyScreen.js
import { renderDOM } from '../../msec/framework/dom.js';
import { getState, setState } from '../../msec/framework/state.js';
import { navigate } from '../../msec/framework/router.js';
import { subscribeToState, sendChat } from '../multiplayer/sync.js';

let lastPlayerList = '';
let lastCount = 0;
let lastPhase = '';
let lastCountdown = 0;

export function lobbyScreen() {
  const root = document.getElementById('app');
  let started = false;

  function renderLobby(players, count, countdown, waitingTime, chatMessages) {
    const vdom = {
      tag: 'div',
      attrs: { class: 'lobby-container' },
      children: [
        {tag: 'div', attrs: { class: 'lobby-screen' }, children: [
          { tag: 'h2', attrs: {}, children: ['Lobby'] },
          { tag: 'div', attrs: {}, children: [`Players: ${count}/4`] },
          {
            tag: 'ul',
            attrs: { class: 'player-list' },
            children: players.map(p => ({
              tag: 'li',
              attrs: {},
              children: [p.nickname],
            })),
          },
          // Show waiting time or countdown
          waitingTime > 0 ?
            { tag: 'div', attrs: { class: 'lobby-waiting' }, children: [`Waiting for more players: ${waitingTime}s`] } :
            { tag: 'div', attrs: { class: 'lobby-countdown' }, children: [`Game starts in: ${countdown}s`] },

          // Chat section
          // {
          //   tag: 'div',
          //   attrs: { class: 'lobby-chat', style: 'margin-top: 20px; border: 1px solid #666; border-radius: 8px; padding: 10px; max-height: 200px; overflow-y: auto;' },
          //   children: [
          //     { tag: 'h4', attrs: { style: 'margin: 0 0 10px 0; color: #ccc;' }, children: ['Chat'] },
          //     {
          //       tag: 'div',
          //       attrs: { class: 'chat-messages', id: 'lobby-chat-messages', style: 'min-height: 100px; max-height: 120px; overflow-y: auto; margin-bottom: 10px; padding: 5px; background: #333; border-radius: 4px;' },
          //       children: (chatMessages || []).slice(-8).map(msg => ({
          //         tag: 'div',
          //         attrs: { style: 'margin-bottom: 5px; font-size: 12px;' },
          //         children: [
          //           {
          //             tag: 'span',
          //             attrs: { style: 'color: #4af; font-weight: bold;' },
          //             children: [`${msg.nickname}: `]
          //           },
          //           {
          //             tag: 'span',
          //             attrs: { style: 'color: #fff;' },
          //             children: [msg.text]
          //           }
          //         ]
          //       }))
          //     },
          //     {
          //       tag: 'form',
          //       attrs: { id: 'lobby-chat-form', style: 'display: flex; gap: 5px;' },
          //       children: [
          //         {
          //           tag: 'input',
          //           attrs: {
          //             type: 'text',
          //             id: 'lobby-chat-input',
          //             placeholder: 'Type a message...',
          //             style: 'flex: 1; padding: 5px; border: 1px solid #666; border-radius: 4px; background: #222; color: #fff;',
          //             maxlength: '100'
          //           }
          //         },
          //         {
          //           tag: 'button',
          //           attrs: {
          //             type: 'submit',
          //             style: 'padding: 5px 10px; border: 1px solid #666; border-radius: 4px; background: #4af; color: #fff; cursor: pointer;'
          //           },
          //           children: ['Send']
          //         }
          //       ]
          //     }
          //   ]
          // },
          {
            tag: 'div',
            attrs: { class: 'links-demo', style: 'margin-top: 20px; font-size: 14px;' },
            children: [
              {
                tag: 'a',
                attrs: { href: '/', style: 'color: #4af; margin-right: 10px;' },
                children: ['Back to Nickname (Internal)']
              },
              {
                tag: 'a',
                attrs: { href: '/game', style: 'color: #4af; margin-right: 10px;' },
                children: ['Go to Game (Internal)']
              },
            ],
          },
        ]},
        // new chat
            {
              tag: 'div',
              attrs: { class: 'chat-container' },
              children: [
                {
                  tag: 'div',
                  attrs: { class: 'chat-drawer' },
                  children: [
                    { tag: 'p', attrs: { class: 'player-font' }, children: ['Chat'] }
                  ]
                },
                {
                tag: 'div',
                attrs: { class: 'chat-sidebar' },
                children: [
                  {
                    tag: 'div',
                    attrs: { class: 'chat-messages' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'chat-container-inner' },
                        children: Array(11).fill().map(() => ({
                          tag: 'div',
                          attrs: { class: 'chat-message' },
                          children: [
                            { tag: 'span', attrs: { class: 'player-font' }, children: ['Player1:'] },
                            { tag: 'p', children: ['Hello, ready to play?'] }
                          ]
                        }))
                      }
                    ]
                  },
                  {
                    tag: 'div',
                    attrs: { class: 'input-form' },
                    children: [
                      {
                        tag: 'form',
                        attrs: { action: '#', method: 'post' },
                        children: [
                          {
                            tag: 'input',
                            attrs: {
                              type: 'text',
                              placeholder: 'Message',
                              class: 'chat-input'
                            }
                          },
                          {
                            tag: 'button',
                            attrs: { type: 'submit', class: 'send-button' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/send.png',
                                  alt: 'Send Icon'
                                }
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          },

      ],
    };
    renderDOM(vdom, root);
    setupChatToggle();
    setupChatInput();

  function setupChatToggle() {
    const chatDrawer = document.querySelector('.chat-drawer');
    const chatContainer = document.querySelector('.chat-container');
    if (chatDrawer && chatContainer) {
      chatDrawer.addEventListener('click', () => {
        chatContainer.classList.toggle('closed');
      });
    }
  }

  function setupChatInput() {
    const chatForm = document.querySelector('.input-form form');
    const chatInput = document.querySelector('.chat-input');

    if (chatForm && chatInput) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const message = chatInput.value.trim();
        if (message) {
          sendChat(message);
          chatInput.value = '';
        }
      });
    }
  }

    // Setup chat form handler
    const chatForm = document.getElementById('lobby-chat-form');
    const chatInput = document.getElementById('lobby-chat-input');

    if (chatForm && chatInput) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const message = chatInput.value.trim();
        if (message) {
          sendChat(message);
          chatInput.value = '';
        }
      });
    }
  }

  // Listen for state updates from server
  subscribeToState((state) => {
    const playerListStr = (state.players || []).map(p => p.id).join(',');
    const count = (state.players || []).length;
    const phase = state.phase;
    const cd = state.lobbyCountdown || 0;
    const wt = state.waitingTime || 0;
    const chatStr = JSON.stringify(state.chat || []);

    // Only skip re-render if all relevant data is unchanged
    if (playerListStr === lastPlayerList && count === lastCount && phase === lastPhase && cd === lastCountdown) {
      return;
    }

    lastPlayerList = playerListStr;
    lastCount = count;
    lastPhase = phase;
    lastCountdown = cd;

    // Only navigate to game when server changes phase to 'game' (after countdown completes)
    if (state.phase === 'game' && Array.isArray(state.map) && !started) {
      console.log('[LobbyScreen] Game phase detected, navigating to game');
      started = true;
      if (window.location.pathname !== '/game') {
        navigate('/game');
      }
      return;
    }

    // Always render the lobby when in lobby phase
    if (state.phase === 'lobby') {
      renderLobby(state.players || [], count, cd, wt, state.chat || []);
    }
  });

  // Initial render
  const state = getState();
  renderLobby(state.players || [], (state.players || []).length, state.lobbyCountdown || 0, state.waitingTime || 0, state.chat || []);
}
